#include "JoystickShield.h"

// 构造函数
JoystickShield::JoystickShield() {
    // 设置默认引脚
    joystickXPin = DEFAULT_JOYSTICK_X_PIN;
    joystickYPin = DEFAULT_JOYSTICK_Y_PIN;
    joystickKPin = DEFAULT_JOYSTICK_K_PIN;
    buttonAPin = DEFAULT_BUTTON_A_PIN;
    buttonBPin = DEFAULT_BUTTON_B_PIN;
    buttonCPin = DEFAULT_BUTTON_C_PIN;
    buttonDPin = DEFAULT_BUTTON_D_PIN;
    buttonEPin = DEFAULT_BUTTON_E_PIN;
    buttonFPin = DEFAULT_BUTTON_F_PIN;

    // 初始化变量
    centerX = 512;
    centerY = 512;
    deadZone = 50;

    // 初始化按钮状态
    for (int i = 0; i < 7; i++) {
        lastButtonStates[i] = false;
        currentButtonStates[i] = false;
        lastDebounceTime[i] = 0;
    }

    // 自动调用初始化
    begin();
}

// 初始化方法
void JoystickShield::begin() {
    // 设置按钮引脚为输入并启用内部上拉电阻
    pinMode(joystickKPin, INPUT_PULLUP);
    pinMode(buttonAPin, INPUT_PULLUP);
    pinMode(buttonBPin, INPUT_PULLUP);
    pinMode(buttonCPin, INPUT_PULLUP);
    pinMode(buttonDPin, INPUT_PULLUP);
    pinMode(buttonEPin, INPUT_PULLUP);
    pinMode(buttonFPin, INPUT_PULLUP);
}

// 校准摇杆中心位置
void JoystickShield::calibrateJoystick() {
    delay(100);  // 等待稳定
    
    // 多次采样取平均值
    long sumX = 0, sumY = 0;
    const int samples = 10;
    
    for (int i = 0; i < samples; i++) {
        sumX += analogRead(joystickXPin);
        sumY += analogRead(joystickYPin);
        delay(10);
    }
    
    centerX = sumX / samples;
    centerY = sumY / samples;
    
    Serial.print("Joystick calibrated - Center X: ");
    Serial.print(centerX);
    Serial.print(", Center Y: ");
    Serial.println(centerY);
}

// 设置摇杆引脚
void JoystickShield::setJoystickPins(int xPin, int yPin) {
    joystickXPin = xPin;
    joystickYPin = yPin;
}

// 设置按钮引脚
void JoystickShield::setButtonPins(int kPin, int aPin, int bPin, int cPin, int dPin, int fPin, int ePin) {
    joystickKPin = kPin;
    buttonAPin = aPin;
    buttonBPin = bPin;
    buttonCPin = cPin;
    buttonDPin = dPin;
    buttonEPin = ePin;
    buttonFPin = fPin;
    
    // 重新初始化引脚
    begin();
}

// 设置死区
void JoystickShield::setDeadZone(int zone) {
    deadZone = zone;
}

// 更新按钮状态（防抖处理）
void JoystickShield::updateButtonStates() {
    unsigned long currentTime = millis();
    
    // 按钮引脚数组
    int buttonPins[7] = {joystickKPin, buttonAPin, buttonBPin, buttonCPin, buttonDPin, buttonEPin, buttonFPin};
    
    for (int i = 0; i < 7; i++) {
        bool reading = !digitalRead(buttonPins[i]);  // 反转读取（因为使用上拉电阻）
        
        if (reading != lastButtonStates[i]) {
            lastDebounceTime[i] = currentTime;
        }
        
        if ((currentTime - lastDebounceTime[i]) > DEBOUNCE_DELAY) {
            if (reading != currentButtonStates[i]) {
                currentButtonStates[i] = reading;
            }
        }
        
        lastButtonStates[i] = reading;
    }
}

// 检查按钮是否被按下（边沿检测）
bool JoystickShield::isButtonPressed(int buttonIndex) {
    static bool lastStates[7] = {false};
    bool currentState = currentButtonStates[buttonIndex];
    bool pressed = currentState && !lastStates[buttonIndex];
    lastStates[buttonIndex] = currentState;
    return pressed;
}

// 事件处理（必须在主循环中调用）
void JoystickShield::processEvents() {
    updateButtonStates();
}

// 摇杆方向检测
bool JoystickShield::isUp() {
    int y = analogRead(joystickYPin);
    return (y - centerY) > JOYSTICK_THRESHOLD;
}

bool JoystickShield::isDown() {
    int y = analogRead(joystickYPin);
    return (centerY - y) > JOYSTICK_THRESHOLD;
}

bool JoystickShield::isLeft() {
    int x = analogRead(joystickXPin);
    return (centerX - x) > JOYSTICK_THRESHOLD;
}

bool JoystickShield::isRight() {
    int x = analogRead(joystickXPin);
    return (x - centerX) > JOYSTICK_THRESHOLD;
}

bool JoystickShield::isRightUp() {
    return isRight() && isUp();
}

bool JoystickShield::isRightDown() {
    return isRight() && isDown();
}

bool JoystickShield::isLeftUp() {
    return isLeft() && isUp();
}

bool JoystickShield::isLeftDown() {
    return isLeft() && isDown();
}

bool JoystickShield::isCenter() {
    int x = analogRead(joystickXPin);
    int y = analogRead(joystickYPin);
    return (abs(x - centerX) <= deadZone) && (abs(y - centerY) <= deadZone);
}

bool JoystickShield::isNotCenter() {
    return !isCenter();
}

// 按钮检测
bool JoystickShield::isJoystickButton() {
    return isButtonPressed(0);  // K按钮
}

bool JoystickShield::isUpButton() {
    return isButtonPressed(1);  // A按钮
}

bool JoystickShield::isRightButton() {
    return isButtonPressed(2);  // B按钮
}

bool JoystickShield::isDownButton() {
    return isButtonPressed(3);  // C按钮
}

bool JoystickShield::isLeftButton() {
    return isButtonPressed(4);  // D按钮
}

bool JoystickShield::isEButton() {
    return isButtonPressed(5);  // E按钮
}

bool JoystickShield::isFButton() {
    return isButtonPressed(6);  // F按钮
}

// 摇杆模拟值
int JoystickShield::xAmplitude() {
    return analogRead(joystickXPin) - centerX;
}

int JoystickShield::yAmplitude() {
    return analogRead(joystickYPin) - centerY;
}

int JoystickShield::xRaw() {
    return analogRead(joystickXPin);
}

int JoystickShield::yRaw() {
    return analogRead(joystickYPin);
}

// 工具方法
void JoystickShield::printStatus() {
    Serial.print("X: ");
    Serial.print(xAmplitude());
    Serial.print(" Y: ");
    Serial.print(yAmplitude());
    Serial.print(" Buttons: ");
    for (int i = 0; i < 7; i++) {
        Serial.print(currentButtonStates[i] ? "1" : "0");
    }
    Serial.println();
}

void JoystickShield::printCalibration() {
    Serial.print("Center X: ");
    Serial.print(centerX);
    Serial.print(", Center Y: ");
    Serial.print(centerY);
    Serial.print(", Dead Zone: ");
    Serial.println(deadZone);
}
