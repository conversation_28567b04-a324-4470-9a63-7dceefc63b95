#include <JoystickShield.h>        // 引入 JoystickShield 库

JoystickShield joystickShield;     // 创建 JoystickShield 对象实例

void setup() {
  Serial.begin(9600);              // 启动串口通信（波特率 9600）
  delay(100);                      // 稍作延迟等待模块初始化

  joystickShield.calibrateJoystick(); // 校准摇杆中心位置（建议在启动时做一次）

  // 如果使用非默认引脚，可取消注释并自定义：
  // joystickShield.setJoystickPins(0, 1);       // 设置摇杆X/Y轴引脚
  // joystickShield.setButtonPins(8, 2, 3, 4, 5, 7, 6); // 设置按钮引脚（K, A, B, C, D, F, E）
}

void loop() {
  joystickShield.processEvents();  // 每次循环必须处理事件

  // 摇杆方向检测（8方向）
  if (joystickShield.isUp())         Serial.println("Up");
  if (joystickShield.isRightUp())    Serial.println("RightUp");
  if (joystickShield.isRight())      Serial.println("Right");
  if (joystickShield.isRightDown())  Serial.println("RightDown");
  if (joystickShield.isDown())       Serial.println("Down");
  if (joystickShield.isLeftDown())   Serial.println("LeftDown");
  if (joystickShield.isLeft())       Serial.println("Left");
  if (joystickShield.isLeftUp())     Serial.println("LeftUp");

  // 摇杆中键按压（按钮K）
  if (joystickShield.isJoystickButton()) Serial.println("Joystick Clicked");

  // 数字按钮检测（A、B、C、D）
  if (joystickShield.isUpButton())     Serial.println("Up Button Clicked");    // A
  if (joystickShield.isRightButton())  Serial.println("Right Button Clicked"); // B
  if (joystickShield.isDownButton())   Serial.println("Down Button Clicked");  // C
  if (joystickShield.isLeftButton())   Serial.println("Left Button Clicked");  // D

  // 扩展按钮 E、F
  if (joystickShield.isEButton())      Serial.println("E Button Clicked");
  if (joystickShield.isFButton())      Serial.println("F Button Clicked");

  // 检测是否偏离中心（即不是居中位置）
  if (joystickShield.isNotCenter())    Serial.println("NotCenter");

  // 显示摇杆当前幅度（坐标）
  Serial.print("x: ");
  Serial.print(joystickShield.xAmplitude());
  Serial.print("   y: ");
  Serial.println(joystickShield.yAmplitude());

  delay(500); // 每次循环等待 500ms
}
