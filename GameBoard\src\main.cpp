#include <JoystickShield.h>        // 引入 JoystickShield 库

JoystickShield joystickShield;     // 创建 JoystickShield 对象实例

// 游戏状态变量
enum GameMode {
  DEMO_MODE,      // 演示模式：显示所有输入
  GAME_MODE,      // 游戏模式：简化输出
  DEBUG_MODE      // 调试模式：详细信息
};

GameMode currentMode = DEMO_MODE;
unsigned long lastModeSwitch = 0;
const unsigned long MODE_SWITCH_DELAY = 1000;  // 模式切换防抖延迟

// 性能监控
unsigned long lastLoopTime = 0;
unsigned long loopCount = 0;
unsigned long fpsCounter = 0;

void setup() {
  Serial.begin(9600);              // 启动串口通信（波特率 9600）
  delay(100);                      // 稍作延迟等待模块初始化

  Serial.println("=== GameBoard Controller Started ===");
  Serial.println("JoystickShield Library v1.0");
  Serial.println("Calibrating joystick...");

  joystickShield.calibrateJoystick(); // 校准摇杆中心位置（建议在启动时做一次）

  // 如果使用非默认引脚，可取消注释并自定义：
  // joystickShield.setJoystickPins(A0, A1);       // 设置摇杆X/Y轴引脚
  // joystickShield.setButtonPins(8, 2, 3, 4, 5, 7, 6); // 设置按钮引脚（K, A, B, C, D, F, E）

  // 设置死区（可选）
  joystickShield.setDeadZone(30);

  Serial.println("Setup complete!");
  Serial.println("Press F button to switch modes");
  Serial.println("Modes: DEMO -> GAME -> DEBUG -> DEMO");
  Serial.println("=====================================");

  lastLoopTime = millis();
}

void loop() {
  joystickShield.processEvents();  // 每次循环必须处理事件

  // 模式切换检测（F按钮）
  if (joystickShield.isFButton() && (millis() - lastModeSwitch > MODE_SWITCH_DELAY)) {
    switchMode();
    lastModeSwitch = millis();
  }

  // 根据当前模式执行不同的逻辑
  switch (currentMode) {
    case DEMO_MODE:
      runDemoMode();
      break;
    case GAME_MODE:
      runGameMode();
      break;
    case DEBUG_MODE:
      runDebugMode();
      break;
  }

  // 性能监控
  updatePerformanceStats();

  // 适当的延迟
  delay(currentMode == DEBUG_MODE ? 100 : 200);
}

// 模式切换函数
void switchMode() {
  switch (currentMode) {
    case DEMO_MODE:
      currentMode = GAME_MODE;
      Serial.println("\n=== GAME MODE ===");
      Serial.println("Simplified output for gaming");
      break;
    case GAME_MODE:
      currentMode = DEBUG_MODE;
      Serial.println("\n=== DEBUG MODE ===");
      Serial.println("Detailed debugging information");
      break;
    case DEBUG_MODE:
      currentMode = DEMO_MODE;
      Serial.println("\n=== DEMO MODE ===");
      Serial.println("Full input demonstration");
      break;
  }
  Serial.println("Press F to switch modes");
  Serial.println("========================");
}

// 演示模式：显示所有输入
void runDemoMode() {
  bool anyInput = false;

  // 摇杆方向检测（8方向）
  if (joystickShield.isUp()) {
    Serial.println("Direction: Up");
    anyInput = true;
  }
  if (joystickShield.isRightUp()) {
    Serial.println("Direction: RightUp");
    anyInput = true;
  }
  if (joystickShield.isRight()) {
    Serial.println("Direction: Right");
    anyInput = true;
  }
  if (joystickShield.isRightDown()) {
    Serial.println("Direction: RightDown");
    anyInput = true;
  }
  if (joystickShield.isDown()) {
    Serial.println("Direction: Down");
    anyInput = true;
  }
  if (joystickShield.isLeftDown()) {
    Serial.println("Direction: LeftDown");
    anyInput = true;
  }
  if (joystickShield.isLeft()) {
    Serial.println("Direction: Left");
    anyInput = true;
  }
  if (joystickShield.isLeftUp()) {
    Serial.println("Direction: LeftUp");
    anyInput = true;
  }

  // 按钮检测
  if (joystickShield.isJoystickButton()) {
    Serial.println("Button: Joystick (K) Clicked");
    anyInput = true;
  }
  if (joystickShield.isUpButton()) {
    Serial.println("Button: A (Up) Clicked");
    anyInput = true;
  }
  if (joystickShield.isRightButton()) {
    Serial.println("Button: B (Right) Clicked");
    anyInput = true;
  }
  if (joystickShield.isDownButton()) {
    Serial.println("Button: C (Down) Clicked");
    anyInput = true;
  }
  if (joystickShield.isLeftButton()) {
    Serial.println("Button: D (Left) Clicked");
    anyInput = true;
  }
  if (joystickShield.isEButton()) {
    Serial.println("Button: E Clicked");
    anyInput = true;
  }

  // 显示摇杆坐标（仅当有移动时）
  if (joystickShield.isNotCenter()) {
    Serial.print("Joystick - X: ");
    Serial.print(joystickShield.xAmplitude());
    Serial.print(", Y: ");
    Serial.println(joystickShield.yAmplitude());
    anyInput = true;
  }

  // 如果有输入，添加分隔线
  if (anyInput) {
    Serial.println("---");
  }
}

// 游戏模式：简化输出，适合游戏使用
void runGameMode() {
  // 摇杆输入（简化为4方向）
  if (joystickShield.isUp() || joystickShield.isRightUp() || joystickShield.isLeftUp()) {
    Serial.println("↑");
  }
  else if (joystickShield.isDown() || joystickShield.isRightDown() || joystickShield.isLeftDown()) {
    Serial.println("↓");
  }
  else if (joystickShield.isLeft()) {
    Serial.println("←");
  }
  else if (joystickShield.isRight()) {
    Serial.println("→");
  }

  // 按钮输入（使用简短标识）
  if (joystickShield.isJoystickButton()) Serial.println("K");
  if (joystickShield.isUpButton()) Serial.println("A");
  if (joystickShield.isRightButton()) Serial.println("B");
  if (joystickShield.isDownButton()) Serial.println("C");
  if (joystickShield.isLeftButton()) Serial.println("D");
  if (joystickShield.isEButton()) Serial.println("E");
}

// 调试模式：详细信息和性能数据
void runDebugMode() {
  static unsigned long lastDebugOutput = 0;

  // 每秒输出一次详细信息
  if (millis() - lastDebugOutput > 1000) {
    Serial.println("=== DEBUG INFO ===");

    // 摇杆原始值和校准信息
    Serial.print("Raw X: ");
    Serial.print(joystickShield.xRaw());
    Serial.print(", Raw Y: ");
    Serial.print(joystickShield.yRaw());
    Serial.print(" | Amplitude X: ");
    Serial.print(joystickShield.xAmplitude());
    Serial.print(", Y: ");
    Serial.println(joystickShield.yAmplitude());

    // 校准信息
    joystickShield.printCalibration();

    // 性能信息
    Serial.print("Loop FPS: ");
    Serial.print(fpsCounter);
    Serial.print(", Free RAM: ");
    Serial.print(getFreeRAM());
    Serial.println(" bytes");

    // 当前状态
    joystickShield.printStatus();

    Serial.println("==================");
    lastDebugOutput = millis();
    fpsCounter = 0;
  }

  // 实时按钮状态（仅在按下时显示）
  if (joystickShield.isJoystickButton()) Serial.println("[DEBUG] Joystick button pressed");
  if (joystickShield.isUpButton()) Serial.println("[DEBUG] A button pressed");
  if (joystickShield.isRightButton()) Serial.println("[DEBUG] B button pressed");
  if (joystickShield.isDownButton()) Serial.println("[DEBUG] C button pressed");
  if (joystickShield.isLeftButton()) Serial.println("[DEBUG] D button pressed");
  if (joystickShield.isEButton()) Serial.println("[DEBUG] E button pressed");
}

// 性能统计更新
void updatePerformanceStats() {
  loopCount++;
  fpsCounter++;

  // 每秒重置计数器
  if (millis() - lastLoopTime > 1000) {
    lastLoopTime = millis();
    loopCount = 0;
  }
}

// 获取可用RAM
int getFreeRAM() {
  extern int __heap_start, *__brkval;
  int v;
  return (int) &v - (__brkval == 0 ? (int) &__heap_start : (int) __brkval);
}
