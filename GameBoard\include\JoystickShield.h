#ifndef JOYSTICKSHIELD_H
#define JOYSTICKSHIELD_H

#include <Arduino.h>

/**
 * JoystickShield 类
 * 用于控制游戏手柄扩展板，支持摇杆和多个按钮
 */
class JoystickShield {
private:
    // 默认引脚定义
    static const int DEFAULT_JOYSTICK_X_PIN = A0;    // 摇杆X轴模拟引脚
    static const int DEFAULT_JOYSTICK_Y_PIN = A1;    // 摇杆Y轴模拟引脚
    static const int DEFAULT_JOYSTICK_K_PIN = 8;     // 摇杆按键K
    static const int DEFAULT_BUTTON_A_PIN = 2;       // 按钮A (上)
    static const int DEFAULT_BUTTON_B_PIN = 3;       // 按钮B (右)
    static const int DEFAULT_BUTTON_C_PIN = 4;       // 按钮C (下)
    static const int DEFAULT_BUTTON_D_PIN = 5;       // 按钮D (左)
    static const int DEFAULT_BUTTON_F_PIN = 7;       // 按钮F
    static const int DEFAULT_BUTTON_E_PIN = 6;       // 按钮E

    // 引脚变量
    int joystickXPin;
    int joystickYPin;
    int joystickKPin;
    int buttonAPin;
    int buttonBPin;
    int buttonCPin;
    int buttonDPin;
    int buttonEPin;
    int buttonFPin;

    // 摇杆校准值
    int centerX;
    int centerY;
    int deadZone;

    // 按钮状态
    bool lastButtonStates[7];  // K, A, B, C, D, E, F
    bool currentButtonStates[7];
    unsigned long lastDebounceTime[7];
    static const unsigned long DEBOUNCE_DELAY = 50;

    // 摇杆阈值
    static const int JOYSTICK_THRESHOLD = 100;

    // 内部方法
    void updateButtonStates();
    bool isButtonPressed(int buttonIndex);
    int getJoystickDirection();

public:
    // 构造函数
    JoystickShield();

    // 初始化方法
    void begin();
    void calibrateJoystick();
    void setJoystickPins(int xPin, int yPin);
    void setButtonPins(int kPin, int aPin, int bPin, int cPin, int dPin, int fPin, int ePin);
    void setDeadZone(int zone);

    // 事件处理
    void processEvents();

    // 摇杆方向检测（8方向）
    bool isUp();
    bool isRightUp();
    bool isRight();
    bool isRightDown();
    bool isDown();
    bool isLeftDown();
    bool isLeft();
    bool isLeftUp();
    bool isCenter();
    bool isNotCenter();

    // 按钮检测
    bool isJoystickButton();    // 摇杆按键K
    bool isUpButton();          // 按钮A
    bool isRightButton();       // 按钮B
    bool isDownButton();        // 按钮C
    bool isLeftButton();        // 按钮D
    bool isEButton();           // 按钮E
    bool isFButton();           // 按钮F

    // 摇杆模拟值
    int xAmplitude();
    int yAmplitude();
    int xRaw();
    int yRaw();

    // 工具方法
    void printStatus();
    void printCalibration();
};

#endif // JOYSTICKSHIELD_H
