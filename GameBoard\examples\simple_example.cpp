/*
 * JoystickShield 简单示例
 * 
 * 这个示例展示了如何使用 JoystickShield 库的基本功能
 * 
 * 硬件连接：
 * - 摇杆 X 轴 -> A0
 * - 摇杆 Y 轴 -> A1
 * - 摇杆按键 K -> Pin 8
 * - 按钮 A -> Pin 2
 * - 按钮 B -> Pin 3
 * - 按钮 C -> Pin 4
 * - 按钮 D -> Pin 5
 * - 按钮 E -> Pin 6
 * - 按钮 F -> Pin 7
 */

#include <JoystickShield.h>

JoystickShield joystick;

void setup() {
    Serial.begin(115200);
    delay(100);
    
    Serial.println("=== JoystickShield 简单示例 ===");
    Serial.println("正在校准摇杆...");
    
    // 校准摇杆中心位置
    joystick.calibrateJoystick();
    
    // 可选：设置死区以避免摇杆漂移
    joystick.setDeadZone(30);
    
    Serial.println("初始化完成！");
    Serial.println("请操作摇杆和按钮进行测试");
    Serial.println("==============================");
}

void loop() {
    // 必须调用此函数来处理输入事件
    joystick.processEvents();
    
    // 检测摇杆方向（8方向）
    if (joystick.isUp()) {
        Serial.println("摇杆：向上");
    }
    if (joystick.isDown()) {
        Serial.println("摇杆：向下");
    }
    if (joystick.isLeft()) {
        Serial.println("摇杆：向左");
    }
    if (joystick.isRight()) {
        Serial.println("摇杆：向右");
    }
    if (joystick.isRightUp()) {
        Serial.println("摇杆：右上");
    }
    if (joystick.isRightDown()) {
        Serial.println("摇杆：右下");
    }
    if (joystick.isLeftUp()) {
        Serial.println("摇杆：左上");
    }
    if (joystick.isLeftDown()) {
        Serial.println("摇杆：左下");
    }
    
    // 检测摇杆按键
    if (joystick.isJoystickButton()) {
        Serial.println("按钮：摇杆按键 (K) 被按下");
    }
    
    // 检测方向按钮
    if (joystick.isUpButton()) {
        Serial.println("按钮：A (上) 被按下");
    }
    if (joystick.isRightButton()) {
        Serial.println("按钮：B (右) 被按下");
    }
    if (joystick.isDownButton()) {
        Serial.println("按钮：C (下) 被按下");
    }
    if (joystick.isLeftButton()) {
        Serial.println("按钮：D (左) 被按下");
    }
    
    // 检测扩展按钮
    if (joystick.isEButton()) {
        Serial.println("按钮：E 被按下");
    }
    if (joystick.isFButton()) {
        Serial.println("按钮：F 被按下");
    }
    
    // 显示摇杆坐标（仅当摇杆不在中心位置时）
    if (joystick.isNotCenter()) {
        Serial.print("摇杆坐标 - X: ");
        Serial.print(joystick.xAmplitude());
        Serial.print(", Y: ");
        Serial.println(joystick.yAmplitude());
    }
    
    // 适当延迟
    delay(100);
}
